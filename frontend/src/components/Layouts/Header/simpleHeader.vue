<template>
  <div>
    <div class="top-nav-header" :class="navTheme === 'dark'?'dark':'light'">
      <div class="top-nav-header-main">
        <div class="top-nav-header-left">
          <div class="top-nav-header-logo">
            <img class="anim animated" src="@/assets/images/logo4.png" style="width: 112px;height: 28px;">
<!--            <span class="hr top-title">价值在线</span>-->
            <span class="hr top-title"><span style="color:#727272">|</span> 上市公司及股东一体化服务平台</span>
          </div>
        </div>
        <div class="top-nav-header-right">
          <span class="top-nav-header-agreement" @click="showAgreement = true">用户协议</span>
          <el-dropdown trigger="click">
            <span style="white-space: nowrap">
                <i class="el-icon-user-solid"></i>
                <span class="el-dropdown-link header-index-action" style="padding-right: 40px">
                  {{getNickName}}<i class="el-icon-caret-bottom el-icon--right"></i>
                </span>
            </span>
            <el-dropdown-menu slot="dropdown">
<!--              <el-dropdown-item>-->
<!--                <span>{{getRemark}}</span>-->
<!--              </el-dropdown-item>-->
              <el-dropdown-item @click.native="updatePassword" v-if="userType !== '0'">
                <i class="el-icon-edit"></i>修改密码
              </el-dropdown-item>
              <el-dropdown-item @click.native="handleLogout">
                <i class="iconfont ic-close"></i>退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>
    <update-password :isShow="showModal" @updatePasswordModalClose="updatePasswordModalClose"></update-password>
    <el-dialog :visible.sync="showAgreement" width="730px" v-if="showAgreement" custom-class="agreement-dialog"
               :close-on-click-modal="false"  :close-on-press-escape="false" :append-to-body="true" :show-close="false"
    >
      <div class="temp-div">
        <img style="position: absolute;right: 0;bottom: 0;pointer-events: none" :src="dbUrl" width="276px" alt="">
        <div class="text-center" style="font-size:18px;font-weight:500;margin-bottom: 24px;color: #222222;">
          用户协议
        </div>
        <div style="max-height:440px;overflow-y: auto;font-size: 14px;font-weight: 400;color: #666666" v-html="agreementContent"></div>
        <div class="" slot="footer" style="text-align:center;margin-top: 24px;">
          <el-button size="small" type="primary" @click="showAgreement = false">确定
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { baseMixin } from '@/store/app-mixin'
import updatePassword from '../../../components/Modal/updatePassword'
import store from '@/store'
import { showConfirm } from "stock-vue-plugin/lib/utils/message";
import {_logout} from "@/api/user-api";
import { _getAgreement } from "@/api/agreement-api";

export default {
  name: 'headerLayout',
  props: {
    isCollapse: Boolean
  },
  mixins: [baseMixin],
  components: { updatePassword },
  data () {
    return {
      showModal: false,
      messageList: [],
      // 是否有未读的消息
      hasUnreadMessage: false,
      getNickName: '',
      userType: '',
      showAgreement: false,
      dbUrl: require("../../../assets/images/image-db.png"),
      agreementContent: ''
    }
  },
  computed: {
    /** 获取昵称 **/
    // getNickName() {
    //     return window.localStorage.getItem('platform_jurisdictionData',personName)
    // },
    /** 获取头像 **/
    // getAvatarUrl () {
    //   return _downLoadFile(store.state.app.info.pictureUrl)
    // },
    /** 获取备注 **/
    getRemark () {
      return store.state.app.info.remark
    }
  },
  mounted () {
    let date = JSON.parse(window.localStorage.getItem("platform_jurisdictionData"));
    this.userType = date.userType
    this.getNickName = date.personName;
    this.getAgreement()
  },
  created () { },
  methods: {
    getAgreement () {
      _getAgreement().then(res => {
        if (res.data.success) {
          this.agreementContent = res.data.result.find(em => em.agreementType === '1').agreementContent
        }
      })
    },
    close () {
      this.instance.close();
    },

    changeCollapse () {
      this.$emit('changeCollapse')
    },
    /* 退出登录
              * */
    handleLogout () {
      showConfirm(this.$messageText['loginOutContent'], this.$messageText['loginOutTitle'], this, 'logout')
    },
    /* 退出登录
              * */
    logout () {
      _logout().then(() => {
        // mq通知子系统
        // logoutMq()
        let data = JSON.parse(window.localStorage.getItem("platform_jurisdictionData"));
        if (!isNaN(data.orgType)) {
          if (Number(data.orgType) > 3){
            store.commit('SET_TOKEN', {})
            this.removeToken();
            this.$router.push('/user/login');
          } else {
            store.commit('SET_TOKEN', {})
            this.removeToken();
            if (process.env.NODE_ENV !== 'development') {
              let url = encodeURIComponent(window.location.origin + "/ui/platform/user/login");
              let baseUrl =  process.env.VUE_APP_CASURL + "/logout?service=" + process.env.VUE_APP_CASURL + "/login?service=" + url
              window.open(baseUrl, "_self");
            }else {
              this.$router.push('/user/login');
            }
          }
        }

      })
    },
    /* 更新密码
              * */
    updatePassword () {
      this.showModal = true;
    },
    /* 关闭更新密码弹出框
              * */
    updatePasswordModalClose () {
      this.showModal = false;
    }
  }
}
</script>

<style lang="scss">
@import './simpleHeader.scss';
</style>
<style scoped>
.top-title {
  color: #333;
  font-family: "Source Han Serif SC";
  font-size: 18px;
  font-style: normal;
  font-weight: 900;
  line-height: 22px; /* 122.222% */
}
.temp-div{
  background: linear-gradient(180deg, #F5F0FF 0%, #FFF 54.48%);
  padding: 24px;
  line-height: 23px;
}
::v-deep .agreement-dialog .el-dialog__body {
  padding: 0 !important;
  border-radius: 16px !important;
}
::v-deep .agreement-dialog .el-dialog__header {
  padding: 0 !important;
}
::v-deep .agreement-dialog .el-dialog__header {
  display: none !important;
}
::v-deep .agreement-dialog {
  border-radius: 16px !important;
}
</style>
