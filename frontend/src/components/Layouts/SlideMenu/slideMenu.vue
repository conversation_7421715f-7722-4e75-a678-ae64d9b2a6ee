<style lang="scss">
    @import 'slideMenu';
</style>
<template>
  <div>
    <el-menu
        :background-color="setBackgroundColor"
        :class="navTheme === 'dark'?'dark':'light'"
        :collapse="isCollapse"
        :default-active="$route.path"
        :mode="mode"
        @close="handleClose"
        @open="handleOpen"
        @select="tabMenu"
        class="menu-layout">
      <slide-item :list="getMenuData"></slide-item>
    </el-menu>
    <choice-user
        :isShow="choiceShow"
        :updateData="choiceInfo"
        title="选择登入账户"
        @choiceClose="choiceClose"
        width="30%"/>
  </div>

</template>
<script>
import slideItem from './slideItem'
import { baseMixin } from '@/store/app-mixin'
import store from '@/store'
import { _getCurrentAudith,_getChildrenInfo,_getRoleListGroupBySystem } from "@/api/user-api";
import { showMessage } from "stock-vue-plugin/lib/utils/message";
import storage from "store";
import { _getOpenedSystems } from "@/api/common-api";
import { _getOrgName,_getOrgNameList } from "@/api/organization-api";
import choiceUser from "./choiceUser.vue";
import EditMQ from "../../../views/mq/editMQ.vue";
import {getSystemStatus} from "../../../api/common-api";
// 引入base64
const Base64 = require("js-base64").Base64;
export default {
  data () {
    return {
      userInfo: {
        key:'',
        userName:'',
        id:'',
        childrenUserName:'',
        userType:'',
      },
      childrenInfo:{},
      choiceInfo:{
        userName:'',
        childrenUserName:'',
        telephone:'',
        orgId:'',
        key:'',
        orgName:'',
        childrenOrgName:'',
      },
      outerMenu: [],
      choiceShow:false,
    }
  },
  props: {
    isCollapse: {
      type: Boolean
    },
    mode: {
      type: String
    }
  },
  components: {EditMQ, slideItem,choiceUser },
  mixins: [baseMixin],
  created () {
    let date = JSON.parse(window.localStorage.getItem("platform_jurisdictionData"));
    this.userInfo = date;
    _getOpenedSystems({}).then(res => {
      if (res.data.success) {
        this.outerMenu = res.data.result;
      }
    })
  },
  computed: {
    setBackgroundColor () {
      if (this.navTheme === 'dark') {
        return '#142751'
      } else {
        return '#fff'
      }
    },
    getMenuData () {
      let info = store.state.app.info;
      if (info.orgType < 4) {
        this.outerMenu.forEach(item => {
          // if (item.menuName === "GQJL") {
          //   item.path = item.path.match(/^[^?]+/)[0];
          // }
        });
      }
      let result = storage.get("platform_menuData").concat(this.outerMenu);
      return result.sort(function (a, b) {
        return a.sortNo - b.sortNo
      })
    }
  },
  mounted () { },
  methods: {
    handleOpen (key, keyPath) {
    },
    handleClose (key, keyPath) {
    },
    async tabMenu (key) {
      let menuName = ''
      this.getMenuData.forEach(item => {
        if (item.path === key) {
          menuName = item.menuName
        }
      })

      // 特殊处理：合规问答菜单改为新开页面
      if (key === '/aitool/wenwen') {
        const jurisdictionData = window.localStorage.getItem("platform_jurisdictionData");
        let filteredJurisdictionData = '{}';

        if (jurisdictionData) {
          const parsedData = JSON.parse(jurisdictionData);
          if (parsedData.menuList && Array.isArray(parsedData.menuList)) {
            parsedData.menuList = parsedData.menuList.filter(item => {
              if (item.menuName === 'aiTool') {
                if (item.menuList && Array.isArray(item.menuList)) {
                  item.menuList = item.menuList.filter(subItem => subItem.menuName === 'wenwen');
                }
                return true;
              }
              return false;
            });
          }
          filteredJurisdictionData = JSON.stringify(parsedData);
        }

        let fullUrl = process.env.VUE_APP_WENWEN + key + '?token=' + store.getters.token
        // 获取当前浏览器域名并与VUE_APP_WENWEN对比，判断是否需要刷新浏览器内存信息
        const currentDomain = window.location.origin;
        const wenwenDomain = new URL(process.env.VUE_APP_WENWEN).origin;
        if (currentDomain !== wenwenDomain) {
          fullUrl = fullUrl + '&platform_jurisdictionData=' + Base64.encode(filteredJurisdictionData || '{}', 'UTF-8')
        }
        console.log("跳转到合规问答:", fullUrl);
        window.open(fullUrl)
        return
      }

      if (menuName === 'HGGL' || menuName === 'GQJL' || menuName === 'TDGF') {
        let status = '1';
        await getSystemStatus({sysCode: menuName}).then(res => {
          status = res.data.result.status;
          if(status !== '1'){
            showMessage('warning', "该系统已锁定请联系管理员解锁！");
          }
        })
        if(status !== '1'){
          return;
        }
      }
      var myArray = [];
      if (this.userInfo.userName === 'admin') {
        myArray = ['HGGL','TDGF','GQJL'];
      }
      var flag = true;
      _getRoleListGroupBySystem({ id: this.userInfo.id, userType: this.userInfo.userType }).then(res => {
        if (res.data.success) {
          let roles = res.data.result.roles;
          let checkedRoleIds = res.data.result.checkedRoleIds;
          checkedRoleIds.forEach(item => {
            roles.forEach(role => {
              if (role.children != undefined) {
                role.children.forEach(child => {
                  if (child.id === item) {
                    myArray.push(child.flag);
                  }
                })
              }
            })
          })
        }
        if (menuName === 'GQJL'){
          if(myArray.indexOf("GQJL") === -1){
            if(flag)  {
              showMessage('warning', "您没有操作该菜单的权限！");
              return;
            }
          }
          _getChildrenInfo({id: this.userInfo.id}).then(res=>{
            if (res.data.success && res.data.result !== null  && res.data.result.childrenUserName !== null && res.data.result.childrenLock === '0'){
              this.childrenInfo = res.data.result;
              this.choiceInfo.key = key;
              this.choiceInfo.userName = this.userInfo.userName;
              // this.choiceInfo.userName = this.userInfo.userName.includes("CSC") ? this.userInfo.userName : "CSC" + this.userInfo.userName;
              this.choiceInfo.childrenUserName = this.childrenInfo.childrenUserName.includes("YCSC") ? this.childrenInfo.childrenUserName : "YCSC" + this.childrenInfo.childrenUserName;
              this.choiceInfo.telephone = this.childrenInfo.telephone;
              this.choiceInfo.childrenOrgId = this.childrenInfo.childrenOrg;
              this.choiceInfo.orgId = this.userInfo.orgId;
              _getOrgName({id: this.userInfo.orgId}).then(res=>{
                this.choiceInfo.orgName = res.data.result;
              })
              _getOrgNameList(this.childrenInfo.childrenOrgList).then(res=>{
                this.choiceInfo.childrenOrgName = res.data.result.join(",");
              })
              this.choiceShow = true;
            }else{
              window.open(key + '&userName=' + this.userInfo.userName);
            }
          })
        }else{
          if (key.includes("http")) {
            if (menuName === 'HGGL' && myArray.indexOf("HGGL") !== -1) {
              flag = false;
            }
            if (menuName === 'TDGF' && myArray.indexOf("TDGF") !== -1) {
              flag = false;
            }
            if(flag)  {
              showMessage('warning', "您没有操作该菜单的权限！");
              return;
            }
            // key = key + '&YTHUserName=' + this.userInfo.userName
            window.open(key);
          } else {
            let param = {
              id: store.state.app.info.isAdmin,
              routerUrl: key
            }
            let authFlag = true;
            _getCurrentAudith(param).then(res => {
              if (res.data.success) {
                authFlag = res.data.result;
              }
              if (!authFlag) {
                showMessage('warning', "您没有操作该菜单的权限！");
              } else {
                this.$router.push({ path: key, query: { token: store.getters.token } });
              }
            })
          }
        }
      })
    },
    choiceClose(){
      this.choiceShow = false;
    }
  }
}
</script>
<style lang='scss' scoped>
  .blue{
    background: #142751;
    span{
      color: #fff;
    }
  }
</style>
<style>
.el-menu-item {
  padding: 0 20px;
}
</style>
