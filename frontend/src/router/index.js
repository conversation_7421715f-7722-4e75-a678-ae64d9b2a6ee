import Vue from 'vue'
import VueRouter from 'vue-router'
/** Screen layout **/
import UserLayout from '@/components/Layouts/ScreenLayout/userLayout'
import BasicLayout from '@/components/Layouts/ScreenLayout/basicLayout'
import SimpleLayout from '@/components/Layouts/ScreenLayout/simpleLayout'

const Layout = {
  name: 'Layout',
  render: (h) => h('el-layout')
}
const _import = require('./_import_' + process.env.NODE_ENV)
/** Home view **/

Vue.use(VueRouter)

export const _ConstantRouterMap = [
  {
    path: '/',
    component: Layout,
    redirect: 'index',
    meta: { title: '服务平台', icon: 'dashboard' },
    children: [
      {
        path: '/user',
        component: UserLayout,
        redirect: '/user/login',
        meta: { title: '登录' },
        children: [
          {
            path: 'login',
            name: 'login',
            component: _import('views/login/login'),
            meta: { title: '登录', noCache: true }
          }
        ]
      },
      {
        path: '',
        name: 'index',
        component: BasicLayout,
        redirect: '/index',
        meta: { title: '系统首页', id: '20' },
        children: [
          {
            path: 'index',
            name: 'index',
            component: _import('views/index/index'),
            meta: { title: '系统首页', icon: 'dashboard', noCache: true, id: '20' }
          }
        ]
      },
      {
        path: '/regulation',
        component: BasicLayout,
        name: 'regulationSearch',
        redirect: '/regulation/regulationSearch',
        meta: { title: '法规检索', id: '11629670878437968080' },
        children: [
          {
            path: 'regulationSearch',
            component: _import('views/regulation/regulationSearch'),
            name: 'regulationSearch',
            meta: { title: '法规检索', id: '11629670878437968080' }
          }
        ]

      },
      {
        path: '/organization',
        component: BasicLayout,
        name: 'organization',
        redirect: '/organization/list',
        meta: { title: '组织架构', id: '48' },
        children: [
          {
            path: 'list',
            name: 'organization_list',
            component: _import('views/organization/department'),
            meta: { title: '组织架构', id: '48' }
          }
        ]
      },
      {
        path: '/person',
        name: 'person',
        component: BasicLayout,
        redirect: '/person/list',
        meta: { title: '内部人员信息', id: '49' },
        children: [
          {
            path: 'list',
            name: 'person_list',
            component: _import('views/person/list'),
            meta: { title: '内部人员信息', id: '49' }
          }
        ]
      },
      {
        path: '/person',
        name: 'outPerson',
        component: BasicLayout,
        redirect: '/person/outList',
        meta: { title: "外部人员信息", id: "748242623978020426" },
        children: [
          {
            path: 'outList',
            name: 'person_outlist',
            component: _import('views/person/outList'),
            meta: { title: '外部人员信息', id: '748242623978020426' }
          }
        ]
      },
      {
        path: '/mq',
        name: 'mqManage',
        component: BasicLayout,
        redirect: '/mq/index',
        meta: { title: '消息管理', id: '100010' },
        children: [
          {
            path: 'index',
            name: 'index',
            component: _import('views/mq/index'),
            meta: { title: '消息管理', id: '100010' }
          }
        ]
      },
      {
        path: '/user',
        component: BasicLayout,
        name: 'user',
        redirect: '/user/list',
        meta: { title: '用户信息', id: '50' },
        children: [
          {
            path: 'list',
            name: 'user_list',
            component: _import('views/user/list'),
            meta: { title: '用户信息', id: '50' }
          }
        ]
      },
      {
        path: '/role',
        component: BasicLayout,
        name: 'role',
        redirect: '/role/list',
        meta: { title: '平台角色信息', id: '52' },
        children: [
          {
            path: 'list',
            name: 'role_list',
            component: _import('views/role/list'),
            meta: { title: '平台角色信息', id: '52' }
          }
        ]
      },
      {
        path: '/system',
        component: BasicLayout,
        name: 'logManageSel',
        redirect: '/system/logManageSel',
        meta: { title: '日志查询', id: '108' },
        children: [
          {
            path: 'logManageSel',
            name: 'log_manage_sel',
            component: _import('views/system/logManageSel'),
            meta: { title: '日志查询', id: '108' }
          }
        ]
      },
      {
        path: '/system',
        component: BasicLayout,
        name: 'saCompany',
        redirect: '/system/saCompany',
        meta: { title: '上市公司管理', id: '54' },
        children: [
          {
            path: 'saCompany',
            name: 'company_list',
            component: _import('views/system/saCompany'),
            meta: { title: '上市公司管理', id: '54' }
          }
        ]
      },
      {
        path: '/system',
        component: BasicLayout,
        name: 'outerSystem',
        redirect: '/system/outerSystem',
        meta: { title: '接入系统管理', id: '11629670878437968009' },
        children: [
          {
            path: 'outerSystem',
            name: 'outerSystem',
            component: _import('views/system/outerSystem'),
            meta: { title: '接入系统管理', id: '11629670878437968009' }
          }
        ]
      },
      {
        path: '/role/outer',
        component: BasicLayout,
        name: 'outerRole',
        redirect: '/role/outer/tabs',
        meta: { title: '接入系统角色', id: '11629670878437979263' },
        children: [
          {
            path: 'tabs',
            name: '接入系统角色',
            component: _import('views/role/outer/tabs'),
            meta: { title: '接入系统角色', id: '11629670878437979263' }
          }
        ]
      },
      {
        path: '/system',
        component: BasicLayout,
        name: 'timedTasks',
        redirect: '/system/timedTasks',
        meta: { title: '定时任务管理', id: '60' },
        children: [
          {
            path: 'timedTasks',
            name: 'timed_tasks',
            component: _import('views/system/timedTasks'),
            meta: { title: '定时任务管理', id: '60' }
          }
        ]
      },
      {
        path: '/aitool',
        component: BasicLayout,
        name: 'aitool',
        // redirect: '/aitool/viewRecords',
        meta: { title: '智能工具', id: '533668241843897703' },
        children: [
          // {
          //   path: 'wenwen',
          //   name: 'wenwen',
          //   component: _import('views/compliance/wenwen/wenwenHomeMain'),
          //   meta: { title: '合规问答', id: '533668241843897717' }
          // },
          {
            path: 'viewRecords',
            name: 'viewRecords',
            component: _import('views/compliance/viewRecords'),
            meta: { title: '问答记录查看', id: '533668241843897723' }
          },
          {
            path: 'feedbackReply',
            name: 'feedbackReply',
            component: _import('views/compliance/feedbackReply'),
            meta: { title: '意见反馈答复', id: '533668241844307017' }
          }
        ]
      },
      // 问问新开页使用新的Layout
      {
        path: '/aitool',
        component: SimpleLayout,
        name: 'aitool',
        children: [
          {
            path: 'wenwen',
            name: 'wenwen',
            component: _import('views/compliance/wenwen/wenwenHomeMain'),
            meta: { title: '合规问答', id: '533668241843897717' }
          }
        ]
      },
      {
        path: '/redirect',
        name: 'redirect',
        component: _import('views/redirect/redirect'),
        meta: { title: '重定向' }
      },
      {
        path: '/error',
        name: 'error',
        component: BasicLayout,
        meta: { title: 'error', hidden: true },
        children: [
          { path: '401', name: '401', redirect: '/user/login', meta: { title: '401', hidden: true } },
          { path: '403', name: '403', redirect: '/user/login', meta: { title: '403', hidden: true } },
          { path: '404', name: '404', component: _import('views/error/404'), meta: { title: '404', hidden: true } },
          { path: '500', name: '500', component: _import('views/error/500'), meta: { title: '500', hidden: true } }
        ]
      },
      {
        path: '*', redirect: '/error/404', hidden: true
      }
    ]
  }
]

const router = new VueRouter({
  base: process.env.VUE_APP_CONTEXT,
  mode: 'history', // require service support
  routes: _ConstantRouterMap,
  scrollBehavior: () => ({ y: 0 })
})

export default router;
