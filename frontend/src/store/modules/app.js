import storage from 'store'
import Cookies from 'js-cookie'
import {
  SIDEBAR_TYPE,
  TOGGLE_NAV_THEME,
  TOGGLE_LAYOUT,
  TOGGLE_FIXED_HEADER,
  TOGGLE_FIXED_SIDEBAR,
  TOGGLE_CONTENT_WIDTH,
  TOGGLE_HIDE_HEADER,
  TOGGLE_COLOR,
  TOGGLE_WEAK,
  TOGGLE_MULTI_TAB,
  // i18n
  APP_LANGUAGE
} from '@/store/mutation-types'
// 引入base64
const Base64 = require("js-base64").Base64;

// 只有当URL是跳转到/aitool/wenwen路由时才走这个逻辑
if (window.location.pathname.concat('/aitool/wenwen')) {
  const urlParams = new URLSearchParams(window.location.search);
  const tempToken = urlParams.get('token');
  const tempJurisdictionData = urlParams.get('platform_jurisdictionData');
  if (tempJurisdictionData && tempJurisdictionData !== "") {
    if (tempToken) {
      Cookies.set('access_token_platform', tempToken)
    }
    if (tempJurisdictionData) {
      // 解码URL编码的jurisdictionData
      const decodedJurisdictionData = Base64.decode(tempJurisdictionData, 'UTF-8');
      // const decodedJurisdictionData = decodeURIComponent(tempJurisdictionData);
      window.localStorage.setItem('platform_jurisdictionData', decodedJurisdictionData);
    }
    if (urlParams.toString()) {
      const token = urlParams.get('token');
      let cleanUrl = window.location.origin + window.location.pathname;
      // 如果有token参数，保留它
      if (token) {
        cleanUrl += '?token=' + encodeURIComponent(token);
      }
      window.history.replaceState({}, document.title, cleanUrl);
    }
  }
}

const app = {
  state: {
    sidebar: {
      opened: !!+Cookies.get('sidebarStatus')
    },
    sideCollapsed: false,
    theme: 'light',
    layout: 'topmenu',
    contentWidth: '',
    fixedHeader: false,
    fixedSidebar: false,
    autoHideHeader: false,
    color: '#14bcf5',
    token: '',
    name: '',
    avatar: '',
    roles: [],
    info: {},
    storeLoading: false,
    menuData: []
  },
  mutations: {
    TOGGLE_SIDEBAR: state => {
      if (state.sidebar.opened) {
        Cookies.set('sidebarStatus', 0)
      } else {
        Cookies.set('sidebarStatus', 1)
      }
      state.sidebar.opened = !state.sidebar.opened
    },
    [SIDEBAR_TYPE]: (state, type) => {
      state.sideCollapsed = type
      storage.set(SIDEBAR_TYPE, type)
    },
    [TOGGLE_NAV_THEME]: (state, theme) => {
      state.theme = theme
      storage.set(TOGGLE_NAV_THEME, theme)
    },
    [TOGGLE_LAYOUT]: (state, mode) => {
      state.layout = mode
      storage.set(TOGGLE_LAYOUT, mode)
    },
    [TOGGLE_FIXED_HEADER]: (state, mode) => {
      state.fixedHeader = mode
      storage.set(TOGGLE_FIXED_HEADER, mode)
    },
    [TOGGLE_FIXED_SIDEBAR]: (state, mode) => {
      state.fixedSidebar = mode
      storage.set(TOGGLE_FIXED_SIDEBAR, mode)
    },
    [TOGGLE_CONTENT_WIDTH]: (state, type) => {
      state.contentWidth = type
      storage.set(TOGGLE_CONTENT_WIDTH, type)
    },
    [TOGGLE_HIDE_HEADER]: (state, type) => {
      state.autoHideHeader = type
      storage.set(TOGGLE_HIDE_HEADER, type)
    },
    [TOGGLE_COLOR]: (state, color) => {
      state.color = color
      storage.set(TOGGLE_COLOR, color)
    },
    [TOGGLE_WEAK]: (state, mode) => {
      state.weak = mode
      storage.set(TOGGLE_WEAK, mode)
    },
    [APP_LANGUAGE]: (state, lang, antd = {}) => {
      state.lang = lang
      state._antLocale = antd
      storage.set(APP_LANGUAGE, lang)
    },
    [TOGGLE_MULTI_TAB]: (state, bool) => {
      storage.set(TOGGLE_MULTI_TAB, bool)
      state.multiTab = bool
    },
    SET_TOKEN: (state, data) => {
      state.token = data.token
      state.info = data.info
    },
    SET_NAME: (state, { name }) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_INFO: (state, info) => {
      state.info = info
    },
    SET_MENU_DATA: (state, data) => {
      state.menuData = data;
    }
  },
  actions: {

    toggleSideBar ({ commit }) {
      commit('TOGGLE_SIDEBAR')
    }

  }
}

export default app
