<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.agreementLog.dao.AgreementBizMapper">
    <select id="getAgreement" resultType="com.stock.service.platform.agreementLog.dto.AgreementDto">
        (
            SELECT
                id AS id,
                agreement_content AS agreementContent,
                agreement_type AS agreementType,
                version AS version
            FROM sa_agreement
            WHERE agreement_type = '1'
            ORDER BY create_time DESC
                LIMIT 1
        )
        UNION ALL
        (
            SELECT
                id AS id,
                agreement_content AS agreementContent,
                agreement_type AS agreementType,
                version AS version
            FROM sa_agreement
            WHERE agreement_type = '2'
            ORDER BY create_time DESC
                LIMIT 1
        )
    </select>
</mapper>