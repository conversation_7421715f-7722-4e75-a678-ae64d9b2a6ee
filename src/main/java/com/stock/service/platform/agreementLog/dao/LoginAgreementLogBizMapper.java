package com.stock.service.platform.agreementLog.dao;

import com.stock.service.platform.agreementLog.dto.LoginAgreementLogDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface LoginAgreementLogBizMapper {

    void saveLoginAgreementLogBatch(List<LoginAgreementLogDto> list);

    /**
     * 查询登录协议日志列表
     */
    List<LoginAgreementLogDto> queryLoginAgreementLogList(LoginAgreementLogDto dto);
}
