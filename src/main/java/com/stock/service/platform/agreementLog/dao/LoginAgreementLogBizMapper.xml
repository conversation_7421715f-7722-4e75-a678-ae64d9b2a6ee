<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.agreementLog.dao.LoginAgreementLogBizMapper">
    <insert id="saveLoginAgreementLogBatch" parameterType="list">
        INSERT INTO sa_login_agreement_log (
        id,
        agreement_id,
        user_id,
        user_name,
        login_ip,
        create_user,
        create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            uuid_short(),
            #{item.agreementId},
            #{item.userId},
            #{item.userName},
            #{item.loginIp},
            #{item.createUser},
            #{item.createTime}
            )
        </foreach>
    </insert>

    <select id="queryLoginAgreementLogList" parameterType="com.stock.service.platform.agreementLog.dto.LoginAgreementLogDto"
            resultType="com.stock.service.platform.agreementLog.dto.LoginAgreementLogDto">
        SELECT
        log.id,
        log.agreement_id AS agreementId,
        agr.version,
        log.user_id AS userId,
        log.user_name AS userName,
        log.login_ip AS loginIp,
        log.create_user AS createUser,
        log.create_time AS createTime,
        log.update_user AS updateUser,
        log.update_time AS updateTime,
        agr.agreement_type AS agreementType
        FROM sa_login_agreement_log log
        LEFT JOIN sa_agreement agr ON log.agreement_id = agr.id
        <where>
            <if test="userName != null and userName != ''">
                AND log.user_name LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="agreementType != null and agreementType != ''">
                AND agr.agreement_type = #{agreementType}
            </if>
            <if test="startTime != null">
                AND log.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND log.create_time &lt; DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>
        </where>
        ORDER BY log.create_time DESC
    </select>
</mapper>