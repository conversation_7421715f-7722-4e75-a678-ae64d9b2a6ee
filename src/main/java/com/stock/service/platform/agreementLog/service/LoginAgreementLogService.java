package com.stock.service.platform.agreementLog.service;

import com.stock.core.service.BaseService;
import com.stock.service.platform.agreementLog.dao.LoginAgreementLogBizMapper;
import com.stock.service.platform.agreementLog.dto.AgreementDto;
import com.stock.service.platform.agreementLog.dto.LoginAgreementLogDto;
import com.stock.service.platform.common.service.CommonService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
@Transactional(rollbackFor = Exception.class)
public class LoginAgreementLogService extends BaseService {

    @Resource
    private LoginAgreementLogBizMapper loginAgreementLogBizMapper;

    public void saveLoginAgreementLog(HttpServletRequest request, List<AgreementDto> agreementDtos) {
        String ip = CommonService.getIp(request);
        List<LoginAgreementLogDto> list = new ArrayList<>();
        LoginAgreementLogDto loginAgreementLog = null;
        for (AgreementDto agreementDto : agreementDtos) {
            loginAgreementLog = new LoginAgreementLogDto();
            loginAgreementLog.setAgreementId(agreementDto.getId());
            loginAgreementLog.setLoginIp(ip);
            loginAgreementLog.setUserId(getUserInfo().getUserId());
            loginAgreementLog.setUserName(getUserInfo().getUsername());
            loginAgreementLog.setCreateUser(getUserInfo().getUserId());
            loginAgreementLog.setCreateTime(new Date());
            list.add(loginAgreementLog);
        }
        loginAgreementLogBizMapper.saveLoginAgreementLogBatch(list);
    }
}
