package com.stock.service.platform.common.dao;

import com.stock.service.platform.compliance.dto.ChatFeedbackDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ChatFeedbackMapper {
    int insertSelective(ChatFeedbackDto record);

    int deleteByChatContentId(String chatContentId);

    List<ChatFeedbackDto> getFeedbackReplyList(ChatFeedbackDto feedbackDto);

    int getFeedbackReplyCount(ChatFeedbackDto feedbackDto);

    int updateIsReplyNewById(String id);

    int updateContinueFeedback(ChatFeedbackDto feedbackDto);

    List<ChatFeedbackDto> getTableList(ChatFeedbackDto feedbackDto);

    int getTotalSize(ChatFeedbackDto feedbackDto);

    List<ChatFeedbackDto> getExportList(ChatFeedbackDto feedbackDto);

    int getIsReplyNewCount(String userName);

    String getIsReply(String id);
}
