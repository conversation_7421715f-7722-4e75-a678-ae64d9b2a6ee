package com.stock.service.platform.common.system.controller;

import com.stock.core.controller.BaseController;
import com.stock.service.platform.agreementLog.dto.LoginAgreementLogDto;
import com.stock.service.platform.common.system.dto.SaLogManageDto;
import com.stock.service.platform.common.system.dto.SaLoginLogManageDto;
import com.stock.service.platform.common.system.dto.SaOperationLogManageDto;
import com.stock.service.platform.common.system.service.SaLogManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("logManage")
public class SaLogManageController extends BaseController {

    @Autowired
    private SaLogManageService saLogManageService;

    /***
     * 根据条件分页查询营业部门
     * For 列表页分页查询
     * @return
     * @param saLogManageParam
     */
    @PostMapping(value = "queryPagingLogManageBySelective")
    @ResponseBody
    public Map<String, Object> queryPagingBusDepBySelective(@RequestBody SaLogManageDto saLogManageParam) {
      Map<String, Object> resultMap = new HashMap<String, Object>();
      Map<String, Object> info =
              super.commonQuery("com.stock.service.platform.common.system.dao.SaLogManageMapper.queryBySelectiveChange",saLogManageParam);
      resultMap.put("tableData", info.get("data"));
      resultMap.put("total", info.get("recordsTotal"));
      return resultMap;
    }

    /***
     * 根据条件分页查询登录/登出日志
     * For 列表页分页查询
     * @return
     * @param saLoginLogManageDto
     */
    @PostMapping(value = "queryLoginLogManageBySelective")
    @ResponseBody
    public Map<String, Object> queryLoginLogManageBySelective(@RequestBody SaLoginLogManageDto saLoginLogManageDto) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Map<String, Object> info =
                super.commonQuery("com.stock.service.platform.common.system.dao.SaLogManageMapper.queryLoginLogManageBySelective",saLoginLogManageDto);
        resultMap.put("tableData", info.get("data"));
        resultMap.put("total", info.get("recordsTotal"));
        return resultMap;
    }

    /***
     * 根据条件分页查询营业部门
     * For 列表页分页查询
     * @return
     * @param saOperationLogManageParam
     */
    @PostMapping(value = "queryOperationLogManageBySelective")
    @ResponseBody
    public Map<String, Object> queryOperationLogManageBySelective(@RequestBody SaOperationLogManageDto saOperationLogManageParam) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Map<String, Object> info = super.commonQuery("com.stock.service.platform.common.system.dao.SaLogManageMapper.queryOperationLogManageBySelective",saOperationLogManageParam);
        resultMap.put("tableData", info.get("data"));
        resultMap.put("total", info.get("recordsTotal"));
        return resultMap;
    }

    /**
     * 查询登录协议日志列表
     */
    @PostMapping("queryLoginAgreementLogList")
    @ResponseBody
    public Map<String, Object> queryLoginAgreementLogList(@RequestBody LoginAgreementLogDto dto) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> info = super.commonQuery("com.stock.service.platform.agreementLog.dao.LoginAgreementLogBizMapper.queryLoginAgreementLogList", dto);
        resultMap.put("tableData", info.get("data"));
        resultMap.put("total", info.get("recordsTotal"));
        return resultMap;
    }

    /***
     * 根据条件导出
     * @return
     * @param saLogManageParam
     */
    @PostMapping(value = "exportExcel")
    public void exportExcel(@RequestBody SaLogManageDto saLogManageParam, HttpServletResponse response) {
        saLogManageService.exportExcel(saLogManageParam,response);
    }
    @PostMapping(value = "loginExportExcel")
    public void loginExportExcel(@RequestBody SaLoginLogManageDto saLoginLogManageDto, HttpServletResponse response) {
        saLogManageService.loginExportExcel(saLoginLogManageDto,response);
    }
    @PostMapping(value = "operationLogExportExcel")
    public void operationLogExportExcel(@RequestBody SaOperationLogManageDto saOperationLogManageParam, HttpServletResponse response) {
        saLogManageService.operationLogExportExcel(saOperationLogManageParam,response);
    }

    @PostMapping(value = "agreementLogExportExcel")
    public void agreementLogExportExcel(@RequestBody LoginAgreementLogDto loginAgreementLogDto, HttpServletResponse response) {
        saLogManageService.agreementLogExportExcel(loginAgreementLogDto,response);
    }

}
