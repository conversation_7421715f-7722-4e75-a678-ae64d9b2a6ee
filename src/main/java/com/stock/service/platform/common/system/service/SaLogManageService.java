package com.stock.service.platform.common.system.service;

import com.alibaba.excel.EasyExcel;
import com.stock.core.service.BaseService;
import com.stock.service.platform.agreementLog.dao.LoginAgreementLogBizMapper;
import com.stock.service.platform.agreementLog.dto.LoginAgreementLogDto;
import com.stock.service.platform.common.system.dao.SaLogManageMapper;
import com.stock.service.platform.common.system.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

@Service
public class SaLogManageService extends BaseService {

    @Autowired
    private SaLogManageMapper saLogManageMapper;

    @Autowired
    private LoginAgreementLogBizMapper loginAgreementLogMapper;


    /**
     * 根据条件查询日志
     *
     * @param saLogManageParam
     * @return
     */
    public void exportExcel(SaLogManageDto saLogManageParam, HttpServletResponse response) {
        List<SaLogManageDto> listSaLogManageDto = saLogManageMapper.queryBySelectiveChange(saLogManageParam);
        List<ExcelSaLogManageDto> excelList =  new ArrayList<>();
        if(listSaLogManageDto.size() > 0){
            for (SaLogManageDto duct:listSaLogManageDto) {
                ExcelSaLogManageDto excelExcelSaLogManageDto = new ExcelSaLogManageDto();
                excelExcelSaLogManageDto.setCompanyCode(duct.getCompanyCode());
                excelExcelSaLogManageDto.setCompanyName(duct.getCompanyName());
                excelExcelSaLogManageDto.setUserName(duct.getUserName());
                excelExcelSaLogManageDto.setLogIP(duct.getLogIP());
                excelExcelSaLogManageDto.setLogURL(duct.getLogURL());
                excelExcelSaLogManageDto.setLogType(duct.getLogType());
                excelExcelSaLogManageDto.setMethodName(duct.getMethodName());
                excelExcelSaLogManageDto.setParam(duct.getParam());
                excelExcelSaLogManageDto.setCreateTime(duct.getCreateTime());
                excelList.add(excelExcelSaLogManageDto);
            }

        }
        String fileName = "日志信息";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            response.setHeader("fileName", java.net.URLEncoder.encode(fileName + ".xls", "utf-8"));
            OutputStream out = response.getOutputStream();
            EasyExcel.write(out, ExcelSaLogManageDto.class).sheet("日志信息").doWrite(excelList);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    public void loginExportExcel(SaLoginLogManageDto saLoginLogManageDto, HttpServletResponse response) {
        List<SaLoginLogManageDto> listSaLogManageDto = saLogManageMapper.queryLoginLogManageBySelective(saLoginLogManageDto);
        List<ExcelSaLoginLogManageDto> excelList =  new ArrayList<>();
        if(listSaLogManageDto.size() > 0){
            for (SaLoginLogManageDto duct:listSaLogManageDto) {
                ExcelSaLoginLogManageDto excelExcelSaLogManageDto = new ExcelSaLoginLogManageDto();
                excelExcelSaLogManageDto.setUserName(duct.getUserName());
                excelExcelSaLogManageDto.setLogName(duct.getLogName());
                excelExcelSaLogManageDto.setLogIP(duct.getLogIp());
                excelExcelSaLogManageDto.setLogTime(duct.getLogTime());
                excelList.add(excelExcelSaLogManageDto);
            }

        }
        String fileName = "登录日志信息";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            response.setHeader("fileName", java.net.URLEncoder.encode(fileName + ".xls", "utf-8"));
            OutputStream out = response.getOutputStream();
            EasyExcel.write(out, ExcelSaLoginLogManageDto.class).sheet("日志信息").doWrite(excelList);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    public void operationLogExportExcel(SaOperationLogManageDto saOperationLogManageParam, HttpServletResponse response) {
        List<SaOperationLogManageDto> listSaOperationLogManageDto = saLogManageMapper.queryOperationLogManageBySelective(saOperationLogManageParam);
        List<ExcelSaOperationLogManageDto> excelList =  new ArrayList<>();
        if(listSaOperationLogManageDto.size() > 0){
            for (SaOperationLogManageDto duct:listSaOperationLogManageDto) {
                ExcelSaOperationLogManageDto excelSaOperationLogManageDto = new ExcelSaOperationLogManageDto();
                excelSaOperationLogManageDto.setUserName(duct.getUserName());
                excelSaOperationLogManageDto.setIp(duct.getIp());
                excelSaOperationLogManageDto.setUserType(duct.getUserType());
                excelSaOperationLogManageDto.setOrgName(duct.getOrgName());
                excelSaOperationLogManageDto.setCompanyName(duct.getCompanyName());
                excelSaOperationLogManageDto.setRouter(duct.getRouter());
                excelSaOperationLogManageDto.setOperationTypeDetail(duct.getOperationTypeDetail());
                excelSaOperationLogManageDto.setRemark(duct.getRemark());
                excelSaOperationLogManageDto.setCreateTime(duct.getCreateTime());
                excelList.add(excelSaOperationLogManageDto);
            }

        }
        String fileName = "日志信息";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            response.setHeader("fileName", java.net.URLEncoder.encode(fileName + ".xls", "utf-8"));
            OutputStream out = response.getOutputStream();
            EasyExcel.write(out, ExcelSaOperationLogManageDto.class).sheet("日志信息").doWrite(excelList);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void agreementLogExportExcel(LoginAgreementLogDto loginAgreementLogDto, HttpServletResponse response) {
        List<LoginAgreementLogDto> excelList = loginAgreementLogMapper.queryLoginAgreementLogList(loginAgreementLogDto);
        excelList.forEach(em -> em.setAgreementType("1".equals(em.getAgreementType()) ? "用户协议" : "隐私政策"));
        String fileName = "登录协议日志信息";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            response.setHeader("fileName", java.net.URLEncoder.encode(fileName + ".xls", "utf-8"));
            OutputStream out = response.getOutputStream();
            EasyExcel.write(out, LoginAgreementLogDto.class).sheet("登录协议日志信息").doWrite(excelList);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
